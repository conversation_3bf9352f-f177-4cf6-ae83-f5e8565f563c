# ACME Relay

一个用于 Caddy 服务器的 ACME 证书中继服务，支持通过腾讯云 DNS 进行 DNS-01 验证。

## 功能特性

- 🔒 **安全的证书获取**: 通过 ACME DNS-01 验证自动获取和续期 SSL/TLS 证书
- 🌐 **腾讯云 DNS 集成**: 支持腾讯云 DNS 服务进行域名验证
- 🛡️ **多层安全防护**:
  - IP 白名单（支持精确 IP 和 CIDR 网段）
  - 域名白名单（支持精确匹配、通配符和后缀匹配）
  - 结构化日志记录和审计
- 📊 **详细日志记录**: 记录所有关键操作和安全事件
- ⚡ **高性能**: 本地证书缓存，避免重复申请

## 安全特性

### IP 白名单
- **精确 IP 匹配**: 允许特定 IP 地址访问
- **CIDR 网段匹配**: 支持网段级别的访问控制
- **默认行为**: 如果未配置白名单，则允许所有 IP 访问

### 域名白名单
- **精确匹配**: `example.com` 只匹配该域名
- **通配符匹配**: `*.example.com` 匹配所有子域名
- **后缀匹配**: `.example.com` 匹配该域名及所有子域名
- **默认行为**: 如果未配置白名单，则允许所有域名

### 日志记录
- **结构化日志**: 支持 JSON 和文本格式
- **安全审计**: 记录所有访问尝试和拒绝事件
- **敏感信息保护**: 不记录证书和私钥内容
- **可配置级别**: debug, info, warn, error

## Caddy 集成

本服务专为 Caddy 服务器设计，支持 `get_certificate` 指令：

```
get_certificate http <url>
```

Caddy 会向指定 URL 发送请求，URL 包含以下查询参数：
- `server_name`: SNI 值（域名）
- `signature_schemes`: 签名算法列表
- `cipher_suites`: 密码套件列表

响应必须包含完整的 PEM 证书链和私钥。

## 配置说明

### 基本配置

```yaml
# 服务器配置
server:
  listen_addr: "127.0.0.1:6060"  # 监听地址
  read_header_timeout: 10        # 读取请求头超时时间（秒）

# ACME 配置
acme:
  account_email: "<EMAIL>"  # ACME 账户邮箱
  data_dir: "./data"                       # 数据存储目录
  directory_url: ""                        # ACME 目录 URL（空为 Let's Encrypt 生产环境）
  renew_before_days: 30                    # 证书到期前多少天开始续期
  key_type: "EC256"                        # 证书密钥类型

# DNS 提供商配置
dns:
  tencentcloud:
    secret_id: "your-secret-id"      # 腾讯云 Secret ID
    secret_key: "your-secret-key"    # 腾讯云 Secret Key
  recursive_nameservers:
    - "**************"
    - "*********"
    - "************"

# 日志配置
log:
  level: "info"    # 日志级别: debug, info, warn, error
  format: "json"   # 日志格式: json, text

# 安全配置
security:
  # IP 白名单（精确匹配）
  allowed_ips:
    - "127.0.0.1"
    - "::1"
  # CIDR 白名单（网段匹配）
  allowed_cidrs:
    - "10.0.0.0/8"      # 内网 A 类
    - "**********/12"   # 内网 B 类
    - "***********/16"  # 内网 C 类
  # 域名白名单
  allowed_domains:
    - "example.com"     # 精确匹配
    - "*.example.com"   # 通配符匹配
    - ".example.org"    # 后缀匹配
```

### 安全配置详解

#### IP 白名单配置
```yaml
security:
  allowed_ips:
    - "*************"    # 允许特定 IP
    - "********"         # 允许另一个 IP
  allowed_cidrs:
    - "***********/16"   # 允许整个 192.168.x.x 网段
    - "10.0.0.0/8"       # 允许整个 10.x.x.x 网段
    - "**********/12"    # 允许 172.16.x.x - 172.31.x.x 网段
```

#### 域名白名单配置
```yaml
security:
  allowed_domains:
    - "example.com"        # 只允许 example.com
    - "*.example.com"      # 允许 sub.example.com, deep.sub.example.com 等
    - ".example.org"       # 允许 example.org, sub.example.org 等
    - "test.local"         # 允许 test.local
```

## 使用方法

### 1. 准备配置文件

复制 `conf.yaml.example` 为 `conf.yaml` 并修改相应配置：

```bash
cp conf.yaml.example conf.yaml
# 编辑 conf.yaml，填入你的腾讯云凭证和其他配置
```

### 2. 启动服务

```bash
# 编译
go build -o acme-relay cmd/server/main.go

# 运行
./acme-relay

# 或指定配置文件路径
CONFIG_PATH=/path/to/conf.yaml ./acme-relay
```

### 3. 配置 Caddy

在 Caddy 配置中使用 `get_certificate` 指令：

```
example.com {
    get_certificate http://127.0.0.1:6060/getcert
    # 其他配置...
}
```

## 安全建议

1. **网络隔离**: 服务应只监听内网地址（如 127.0.0.1 或内网 IP）
2. **反向代理**: 通过 Caddy 或 Nginx 等反向代理访问，不要直接暴露到公网
3. **IP 白名单**: 配置严格的 IP 白名单，只允许可信的客户端访问
4. **域名白名单**: 配置域名白名单，防止为未授权域名签发证书
5. **日志监控**: 监控日志中的安全事件和异常访问
6. **定期轮换**: 定期轮换腾讯云 API 密钥

## 日志示例

### 成功的证书请求
```json
{
  "time": "2024-01-01T12:00:00Z",
  "level": "INFO",
  "msg": "Certificate issued/renewed successfully",
  "client_ip": "*************",
  "server_name": "example.com",
  "duration": "5.2s",
  "fullchain_size": 3456,
  "key_size": 1234
}
```

### 被拒绝的请求
```json
{
  "time": "2024-01-01T12:00:00Z",
  "level": "WARN",
  "msg": "Certificate request rejected: IP not in whitelist",
  "client_ip": "*******",
  "server_name": "example.com",
  "allowed_ips": ["127.0.0.1"],
  "allowed_cidrs": ["***********/16"]
}
```
