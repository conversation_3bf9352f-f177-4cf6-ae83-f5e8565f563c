# GitLab CI/CD 配置文件
# ACME Relay 项目

# 定义阶段
stages:
  - test
  - security
  - build
  - deploy

# 全局变量
variables:
  # Go 相关配置
  GO_VERSION: "1.24"
  GOPROXY: "https://goproxy.cn,direct"
  GOSUMDB: "sum.golang.google.cn"
  CGO_ENABLED: "0"
  
  # Docker 相关配置
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  
  # 应用配置
  APP_NAME: "acme-relay"
  REGISTRY: "$CI_REGISTRY"
  IMAGE_TAG: "$CI_REGISTRY_IMAGE:$CI_COMMIT_SHA"
  LATEST_TAG: "$CI_REGISTRY_IMAGE:latest"

# 缓存配置
cache:
  paths:
    - .cache/go-build/
    - .cache/go-mod/

# 模板：Go 环境
.go-template: &go-template
  image: golang:${GO_VERSION}-alpine
  before_script:
    - apk add --no-cache git ca-certificates make
    - export GOCACHE="$CI_PROJECT_DIR/.cache/go-build"
    - export GOMODCACHE="$CI_PROJECT_DIR/.cache/go-mod"
    - go version

# 代码质量检查
lint:
  <<: *go-template
  stage: test
  script:
    - go fmt ./...
    - go vet ./...
    - |
      if [ -n "$(gofmt -l .)" ]; then
        echo "Go code is not formatted:"
        gofmt -d .
        exit 1
      fi
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# 单元测试
test:
  <<: *go-template
  stage: test
  script:
    - go mod download
    - go test -v -race -coverprofile=coverage.out ./...
    - go tool cover -html=coverage.out -o coverage.html
  coverage: '/coverage: \d+\.\d+% of statements/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
    paths:
      - coverage.html
      - coverage.out
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# 依赖安全扫描
dependency-scan:
  <<: *go-template
  stage: security
  script:
    - go mod download
    - go list -json -deps ./... | nancy sleuth
  allow_failure: true
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# 静态安全分析
gosec:
  <<: *go-template
  stage: security
  before_script:
    - apk add --no-cache git ca-certificates
    - go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest
  script:
    - gosec -fmt json -out gosec-report.json ./...
  artifacts:
    reports:
      sast: gosec-report.json
    expire_in: 1 week
  allow_failure: true
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# 构建二进制文件
build-binary:
  <<: *go-template
  stage: build
  script:
    - go mod download
    - mkdir -p dist
    - |
      # 构建多平台二进制文件
      GOOS=linux GOARCH=amd64 go build -ldflags="-w -s" -o dist/${APP_NAME}-linux-amd64 cmd/server/main.go
      GOOS=linux GOARCH=arm64 go build -ldflags="-w -s" -o dist/${APP_NAME}-linux-arm64 cmd/server/main.go
      GOOS=darwin GOARCH=amd64 go build -ldflags="-w -s" -o dist/${APP_NAME}-darwin-amd64 cmd/server/main.go
      GOOS=darwin GOARCH=arm64 go build -ldflags="-w -s" -o dist/${APP_NAME}-darwin-arm64 cmd/server/main.go
      GOOS=windows GOARCH=amd64 go build -ldflags="-w -s" -o dist/${APP_NAME}-windows-amd64.exe cmd/server/main.go
  artifacts:
    paths:
      - dist/
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_TAG
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# 构建 Docker 镜像
build-docker:
  stage: build
  image: docker:24-dind
  services:
    - docker:24-dind
  before_script:
    - echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin "$CI_REGISTRY"
  script:
    - |
      # 构建镜像
      docker build \
        --build-arg BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ') \
        --build-arg VCS_REF=$CI_COMMIT_SHA \
        --build-arg VERSION=${CI_COMMIT_TAG:-$CI_COMMIT_SHORT_SHA} \
        -t $IMAGE_TAG \
        -t $LATEST_TAG \
        .
      
      # 推送镜像
      docker push $IMAGE_TAG
      
      # 如果是主分支或标签，推送 latest
      if [ "$CI_COMMIT_BRANCH" = "$CI_DEFAULT_BRANCH" ] || [ -n "$CI_COMMIT_TAG" ]; then
        docker push $LATEST_TAG
      fi
  rules:
    - if: $CI_COMMIT_TAG
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

# 部署到开发环境
deploy-dev:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache curl
  script:
    - |
      echo "部署到开发环境"
      echo "镜像: $IMAGE_TAG"
      # 这里可以添加实际的部署脚本
      # 例如：调用 Kubernetes API、更新 docker-compose 等
  environment:
    name: development
    url: https://acme-relay-dev.example.com
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  when: manual

# 部署到生产环境
deploy-prod:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache curl
  script:
    - |
      echo "部署到生产环境"
      echo "镜像: $LATEST_TAG"
      # 这里可以添加实际的部署脚本
  environment:
    name: production
    url: https://acme-relay.example.com
  rules:
    - if: $CI_COMMIT_TAG
  when: manual

# 发布 Release
release:
  stage: deploy
  image: registry.gitlab.com/gitlab-org/release-cli:latest
  script:
    - echo "创建 Release"
  release:
    name: 'Release $CI_COMMIT_TAG'
    description: 'Release $CI_COMMIT_TAG'
    tag_name: '$CI_COMMIT_TAG'
    assets:
      links:
        - name: 'Linux AMD64'
          url: '$CI_PROJECT_URL/-/jobs/artifacts/$CI_COMMIT_TAG/raw/dist/${APP_NAME}-linux-amd64?job=build-binary'
        - name: 'Linux ARM64'
          url: '$CI_PROJECT_URL/-/jobs/artifacts/$CI_COMMIT_TAG/raw/dist/${APP_NAME}-linux-arm64?job=build-binary'
        - name: 'macOS AMD64'
          url: '$CI_PROJECT_URL/-/jobs/artifacts/$CI_COMMIT_TAG/raw/dist/${APP_NAME}-darwin-amd64?job=build-binary'
        - name: 'macOS ARM64'
          url: '$CI_PROJECT_URL/-/jobs/artifacts/$CI_COMMIT_TAG/raw/dist/${APP_NAME}-darwin-arm64?job=build-binary'
        - name: 'Windows AMD64'
          url: '$CI_PROJECT_URL/-/jobs/artifacts/$CI_COMMIT_TAG/raw/dist/${APP_NAME}-windows-amd64.exe?job=build-binary'
        - name: 'Docker Image'
          url: '$CI_REGISTRY_IMAGE:$CI_COMMIT_TAG'
  rules:
    - if: $CI_COMMIT_TAG
