package acmex

import (
	"crypto"
	"crypto/ecdsa"
	"crypto/elliptic"
	"crypto/rand"
	"crypto/x509"
	"encoding/json"
	"encoding/pem"
	"errors"
	"os"
	"path/filepath"

	"github.com/go-acme/lego/v4/registration"
)

type user struct {
	Email        string                 `json:"email"`
	Registration *registration.Resource `json:"registration,omitempty"`
	key          crypto.PrivateKey      `json:"-"`
}

func (u *user) GetEmail() string                        { return u.Email }
func (u *user) GetRegistration() *registration.Resource { return u.Registration }
func (u *user) GetPrivateKey() crypto.PrivateKey        { return u.key }

func (m *Manager) loadOrCreateAccount() (*user, error) {
	m.accMu.Lock()
	defer m.accMu.Unlock()

	if m.user != nil {
		return m.user, nil
	}

	accPath := filepath.Join(m.cfg.DataDir, fileAccount)
	keyPath := filepath.Join(m.cfg.DataDir, fileAccountKey)

	// 1) 如果已有账户文件，加载
	if b, err := os.ReadFile(accPath); err == nil {
		u := &user{}
		if err := json.Unmarshal(b, u); err != nil {
			return nil, err
		}
		kb, err := os.ReadFile(keyPath)
		if err != nil {
			return nil, err
		}
		priv, err := parseECPrivateKeyFromPEM(kb)
		if err != nil {
			return nil, err
		}
		u.key = priv
		m.user = u
		return m.user, nil
	}

	// 2) 否则创建一个新的账户密钥（EC P-256）
	priv, err := ecdsa.GenerateKey(elliptic.P256(), rand.Reader)
	if err != nil {
		return nil, err
	}
	u := &user{
		Email: m.cfg.Email,
		key:   priv,
	}

	// 落盘（先写私钥，再写账号）
	keyDER, err := x509.MarshalECPrivateKey(priv)
	if err != nil {
		return nil, err
	}
	keyPEM := pem.EncodeToMemory(&pem.Block{Type: "EC PRIVATE KEY", Bytes: keyDER})
	if err := os.WriteFile(keyPath, keyPEM, 0600); err != nil {
		return nil, err
	}

	if err := SaveAccount(m.cfg.DataDir, u); err != nil {
		return nil, err
	}
	m.user = u
	return m.user, nil
}

func SaveAccount(dataDir string, u *user) error {
	accPath := filepath.Join(dataDir, fileAccount)
	b, err := json.MarshalIndent(struct {
		Email        string                 `json:"email"`
		Registration *registration.Resource `json:"registration,omitempty"`
	}{
		Email:        u.Email,
		Registration: u.Registration,
	}, "", "  ")
	if err != nil {
		return err
	}
	return os.WriteFile(accPath, b, 0600)
}

func parseECPrivateKeyFromPEM(pemBytes []byte) (crypto.PrivateKey, error) {
	block, _ := pem.Decode(pemBytes)
	if block == nil {
		return nil, errors.New("acmex: invalid pem")
	}
	return x509.ParseECPrivateKey(block.Bytes)
}
