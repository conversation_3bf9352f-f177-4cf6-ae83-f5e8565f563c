package handlers

import (
	"fmt"
	"log/slog"
	"net/http"
	"time"

	"code.ixdev.cn/cnix/cbdv/acme-relay/acmex"
	"code.ixdev.cn/cnix/cbdv/acme-relay/internal/config"
	"code.ixdev.cn/cnix/cbdv/acme-relay/internal/security"
	"code.ixdev.cn/cnix/cbdv/acme-relay/internal/utils"
)

// CertHandler 证书处理器
type CertHandler struct {
	config  *config.Config
	manager *acmex.Manager
	logger  *slog.Logger
}

// NewCertHandler 创建新的证书处理器
func NewCertHandler(cfg *config.Config, mgr *acmex.Manager, logger *slog.Logger) *CertHandler {
	return &CertHandler{
		config:  cfg,
		manager: mgr,
		logger:  logger,
	}
}

// GetCert 处理证书获取请求
func (h *CertHandler) GetCert(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()
	clientIP := utils.GetClientIP(r)
	serverName := r.URL.Query().Get("server_name")

	// 记录请求开始
	h.logger.Info("Certificate request received",
		slog.String("client_ip", clientIP),
		slog.String("method", r.Method),
		slog.String("user_agent", r.UserAgent()),
		slog.String("server_name", serverName),
		slog.String("request_id", fmt.Sprintf("%d", startTime.UnixNano())))

	// 检查 IP 白名单
	if !security.IsIPAllowed(clientIP, h.config.Security.AllowedIPs, h.config.Security.AllowedCIDRs) {
		h.logger.Warn("Certificate request rejected: IP not in whitelist",
			slog.String("client_ip", clientIP),
			slog.String("server_name", serverName),
			slog.Any("allowed_ips", h.config.Security.AllowedIPs),
			slog.Any("allowed_cidrs", h.config.Security.AllowedCIDRs))
		http.Error(w, "access denied", http.StatusForbidden)
		return
	}

	if serverName == "" {
		h.logger.Warn("Certificate request rejected: missing server_name",
			slog.String("client_ip", clientIP),
			slog.String("method", r.Method))
		http.Error(w, "missing server_name", http.StatusBadRequest)
		return
	}

	// 检查域名白名单
	if !security.IsDomainAllowed(serverName, h.config.Security.AllowedDomains) {
		h.logger.Warn("Certificate request rejected: domain not in whitelist",
			slog.String("client_ip", clientIP),
			slog.String("server_name", serverName),
			slog.Any("allowed_domains", h.config.Security.AllowedDomains))
		http.Error(w, "domain not allowed", http.StatusForbidden)
		return
	}

	// 生产里你可以校验来源（只允许内网/Caddy），或做域名白名单检查
	ctx := r.Context()

	h.logger.Info("Starting certificate issuance/renewal",
		slog.String("server_name", serverName),
		slog.String("client_ip", clientIP))

	fullchain, key, err := h.manager.GetCertificate(ctx, serverName)
	duration := time.Since(startTime)

	if err != nil {
		h.logger.Error("Certificate issuance/renewal failed",
			slog.String("server_name", serverName),
			slog.String("client_ip", clientIP),
			slog.String("error", err.Error()),
			slog.Duration("duration", duration))
		http.Error(w, "issue/renew failed", http.StatusInternalServerError)
		return
	}

	// 记录成功响应（不记录证书和私钥内容）
	h.logger.Info("Certificate issued/renewed successfully",
		slog.String("server_name", serverName),
		slog.String("client_ip", clientIP),
		slog.Duration("duration", duration),
		slog.Int("fullchain_size", len(fullchain)),
		slog.Int("key_size", len(key)))

	w.Header().Set("Content-Type", "application/x-pem-file")
	w.Header().Set("Cache-Control", "no-store, no-cache, must-revalidate")
	w.Header().Set("Pragma", "no-cache")
	w.WriteHeader(http.StatusOK)
	_, _ = w.Write(fullchain)
	_, _ = w.Write([]byte("\n"))
	_, _ = w.Write(key)
}
