package config

import (
	"os"

	"gopkg.in/yaml.v3"
)

// Config 配置结构体
type Config struct {
	Server struct {
		ListenAddr        string `yaml:"listen_addr"`
		ReadHeaderTimeout int    `yaml:"read_header_timeout"`
	} `yaml:"server"`
	ACME struct {
		AccountEmail    string `yaml:"account_email"`
		DataDir         string `yaml:"data_dir"`
		DirectoryURL    string `yaml:"directory_url"`
		RenewBeforeDays int    `yaml:"renew_before_days"`
		KeyType         string `yaml:"key_type"`
	} `yaml:"acme"`
	DNS struct {
		TencentCloud struct {
			SecretID  string `yaml:"secret_id"`
			SecretKey string `yaml:"secret_key"`
		} `yaml:"tencentcloud"`
		RecursiveNameservers []string `yaml:"recursive_nameservers"`
	} `yaml:"dns"`
	Log struct {
		Level  string `yaml:"level"`  // debug, info, warn, error
		Format string `yaml:"format"` // json, text
	} `yaml:"log"`
	Security struct {
		AllowedIPs     []string `yaml:"allowed_ips"`     // IP 白名单
		AllowedCIDRs   []string `yaml:"allowed_cidrs"`   // CIDR 白名单
		AllowedDomains []string `yaml:"allowed_domains"` // 域名白名单
	} `yaml:"security"`
}

// Load 从配置文件加载配置
func Load(configPath string) (*Config, error) {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, err
	}

	var cfg Config
	if err := yaml.Unmarshal(data, &cfg); err != nil {
		return nil, err
	}

	return &cfg, nil
}

// GetEnv 获取环境变量，如果不存在则返回默认值
func GetEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
