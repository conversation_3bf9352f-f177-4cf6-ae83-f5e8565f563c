package security

import (
	"net"
	"strings"
)

// IsIPAllowed 检查 IP 是否在白名单中
func IsIPAllowed(clientIP string, allowedIPs []string, allowedCIDRs []string) bool {
	// 如果没有配置白名单，则允许所有 IP
	if len(allowedIPs) == 0 && len(allowedCIDRs) == 0 {
		return true
	}

	ip := net.ParseIP(clientIP)
	if ip == nil {
		return false
	}

	// 检查精确 IP 匹配
	for _, allowedIP := range allowedIPs {
		if allowedIP == clientIP {
			return true
		}
	}

	// 检查 CIDR 匹配
	for _, cidr := range allowedCIDRs {
		_, network, err := net.ParseCIDR(cidr)
		if err != nil {
			continue
		}
		if network.Contains(ip) {
			return true
		}
	}

	return false
}

// IsDomainAllowed 检查域名是否在白名单中
func IsDomainAllowed(domain string, allowedDomains []string) bool {
	// 如果没有配置域名白名单，则允许所有域名
	if len(allowedDomains) == 0 {
		return true
	}

	domain = strings.ToLower(strings.TrimSpace(domain))

	for _, allowed := range allowedDomains {
		allowed = strings.ToLower(strings.TrimSpace(allowed))

		// 精确匹配
		if domain == allowed {
			return true
		}

		// 通配符匹配（如 *.example.com）
		if strings.HasPrefix(allowed, "*.") {
			suffix := allowed[2:] // 去掉 "*."
			if strings.HasSuffix(domain, "."+suffix) || domain == suffix {
				return true
			}
		}

		// 后缀匹配（如 .example.com）
		if strings.HasPrefix(allowed, ".") {
			if strings.HasSuffix(domain, allowed) {
				return true
			}
		}
	}

	return false
}
