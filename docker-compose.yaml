services:
  acme-relay:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: acme-relay
    restart: unless-stopped
    ports:
      - "6060:6060"
    volumes:
      - ./conf.yaml:/app/conf.yaml:ro
      - acme-data:/app/data
    networks:
      - acme-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:6060/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  acme-data:
    driver: local

networks:
  acme-network:
    driver: bridge