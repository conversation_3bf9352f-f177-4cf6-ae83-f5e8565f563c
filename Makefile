# Makefile for ACME Relay

# 变量定义
APP_NAME := acme-relay
VERSION := $(shell git describe --tags --always --dirty)
BUILD_TIME := $(shell date -u '+%Y-%m-%d_%H:%M:%S')
GO_VERSION := $(shell go version | cut -d ' ' -f 3)

# Go 相关变量
GOCMD := go
GOBUILD := $(GOCMD) build
GOCLEAN := $(GOCMD) clean
GOTEST := $(GOCMD) test
GOGET := $(GOCMD) get
GOMOD := $(GOCMD) mod

# 构建标志
LDFLAGS := -ldflags "-w -s -X main.version=$(VERSION) -X main.buildTime=$(BUILD_TIME) -X main.goVersion=$(GO_VERSION)"

# 目录
DIST_DIR := dist
BINARY_NAME := $(APP_NAME)
BINARY_UNIX := $(BINARY_NAME)-linux-amd64

# 默认目标
.PHONY: all
all: clean deps test build

# 清理
.PHONY: clean
clean:
	$(GOCLEAN)
	rm -rf $(DIST_DIR)
	rm -f coverage.out coverage.html

# 下载依赖
.PHONY: deps
deps:
	$(GOMOD) download
	$(GOMOD) tidy

# 代码格式化
.PHONY: fmt
fmt:
	go fmt ./...

# 代码检查
.PHONY: vet
vet:
	go vet ./...

# 静态分析
.PHONY: lint
lint: fmt vet
	@if [ -n "$$(gofmt -l .)" ]; then \
		echo "Go code is not formatted:"; \
		gofmt -d .; \
		exit 1; \
	fi

# 运行测试
.PHONY: test
test:
	$(GOTEST) -v -race -coverprofile=coverage.out ./...

# 测试覆盖率
.PHONY: coverage
coverage: test
	$(GOCMD) tool cover -html=coverage.out -o coverage.html
	$(GOCMD) tool cover -func=coverage.out

# 构建本地版本
.PHONY: build
build:
	mkdir -p $(DIST_DIR)
	$(GOBUILD) $(LDFLAGS) -o $(DIST_DIR)/$(BINARY_NAME) cmd/server/main.go

# 构建 Linux 版本
.PHONY: build-linux
build-linux:
	mkdir -p $(DIST_DIR)
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(DIST_DIR)/$(BINARY_UNIX) cmd/server/main.go

# 构建所有平台版本
.PHONY: build-all
build-all:
	mkdir -p $(DIST_DIR)
	# Linux
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(DIST_DIR)/$(BINARY_NAME)-linux-amd64 cmd/server/main.go
	CGO_ENABLED=0 GOOS=linux GOARCH=arm64 $(GOBUILD) $(LDFLAGS) -o $(DIST_DIR)/$(BINARY_NAME)-linux-arm64 cmd/server/main.go
	# macOS
	CGO_ENABLED=0 GOOS=darwin GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(DIST_DIR)/$(BINARY_NAME)-darwin-amd64 cmd/server/main.go
	CGO_ENABLED=0 GOOS=darwin GOARCH=arm64 $(GOBUILD) $(LDFLAGS) -o $(DIST_DIR)/$(BINARY_NAME)-darwin-arm64 cmd/server/main.go
	# Windows
	CGO_ENABLED=0 GOOS=windows GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(DIST_DIR)/$(BINARY_NAME)-windows-amd64.exe cmd/server/main.go

# 运行应用
.PHONY: run
run: build
	./$(DIST_DIR)/$(BINARY_NAME)

# 运行开发模式
.PHONY: dev
dev:
	$(GOCMD) run cmd/server/main.go

# Docker 构建
.PHONY: docker-build
docker-build:
	docker build -t $(APP_NAME):$(VERSION) .
	docker tag $(APP_NAME):$(VERSION) $(APP_NAME):latest

# Docker 运行
.PHONY: docker-run
docker-run:
	docker run -p 6060:6060 -v $(PWD)/conf.yaml:/app/conf.yaml $(APP_NAME):latest

# 安全扫描
.PHONY: security
security:
	@if command -v gosec >/dev/null 2>&1; then \
		gosec ./...; \
	else \
		echo "gosec not installed. Run: go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest"; \
	fi

# 生成模拟数据
.PHONY: mock
mock:
	@if command -v mockgen >/dev/null 2>&1; then \
		mockgen -source=internal/handlers/cert.go -destination=internal/mocks/cert_mock.go; \
	else \
		echo "mockgen not installed. Run: go install github.com/golang/mock/mockgen@latest"; \
	fi

# 基准测试
.PHONY: bench
bench:
	$(GOTEST) -bench=. -benchmem ./...

# 安装工具
.PHONY: install-tools
install-tools:
	go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest
	go install github.com/golang/mock/mockgen@latest
	go install golang.org/x/tools/cmd/goimports@latest

# 帮助信息
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  all          - Clean, download deps, test, and build"
	@echo "  clean        - Clean build artifacts"
	@echo "  deps         - Download and tidy dependencies"
	@echo "  fmt          - Format Go code"
	@echo "  vet          - Run go vet"
	@echo "  lint         - Run linting checks"
	@echo "  test         - Run tests"
	@echo "  coverage     - Generate test coverage report"
	@echo "  build        - Build binary for current platform"
	@echo "  build-linux  - Build Linux binary"
	@echo "  build-all    - Build binaries for all platforms"
	@echo "  run          - Build and run the application"
	@echo "  dev          - Run in development mode"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run   - Run Docker container"
	@echo "  security     - Run security scan"
	@echo "  bench        - Run benchmarks"
	@echo "  install-tools- Install development tools"
	@echo "  help         - Show this help message"
