# ACME Relay 配置文件

# 服务器配置
server:
  # 监听地址，格式: host:port
  listen_addr: "127.0.0.1:6060"
  # 读取请求头超时时间（秒）
  read_header_timeout: 10

# ACME 配置
acme:
  # 账户邮箱（必填）
  account_email: "<EMAIL>"
  # 数据目录，用于存储账户信息和证书
  data_dir: "./data"
  # ACME 目录 URL，留空使用 Let's Encrypt 生产环境
  # 测试环境: https://acme-staging-v02.api.letsencrypt.org/directory
  directory_url: ""
  # 证书到期前多少天开始续期
  renew_before_days: 30
  # 证书密钥类型: EC256, EC384, RSA2048, RSA4096, RSA8192
  key_type: "EC256"

# DNS 提供商配置（当前使用腾讯云）
dns:
  # 腾讯云认证信息
  tencentcloud:
    secret_id: ""     # 腾讯云 Secret ID
    secret_key: ""    # 腾讯云 Secret Key
  # 腾讯云 DNS 递归解析服务器
  recursive_nameservers:
    - "**************"
    - "*********"
    - "************"

# 日志配置
log:
  # 日志级别: debug, info, warn, error
  level: "info"
  # 日志格式: json, text
  format: "json"

# 安全配置
security:
  # IP 白名单（精确匹配）
  allowed_ips:
    - "127.0.0.1"
    - "::1"
  # CIDR 白名单（网段匹配）
  allowed_cidrs:
    - "10.0.0.0/8"      # 内网 A 类
    - "**********/12"   # 内网 B 类
    - "***********/16"  # 内网 C 类
  # 域名白名单
  allowed_domains:
    - "example.com"     # 精确匹配
    - "*.example.com"   # 通配符匹配
    - ".example.org"    # 后缀匹配